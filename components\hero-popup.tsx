"use client"
import { useState, useEffect } from "react"
import { X } from "lucide-react"
import Link from "next/link"

// Import priceTable and amounts from pricing-section.tsx logic
// Fix type for priceTable and amountMap
const priceTable: Record<string, { real: number[]; discount: number[] }> = {
  "INSTANT": {
    real:   [56, 131, 231, 419, 883, 1930, 3430, 5650, 11230],
    discount: [28, 66, 116, 210, 441, 965, 1715, 2825, 5615],
  },
  "HFT PRO": {
    real:   [18, 35, 47, 80, 175, 290, 510, 819, 1490],
    discount: [9, 18, 24, 40, 88, 145, 255, 410, 745],
  },
  "1STEP": {
    real:   [10, 16, 28, 48, 92, 167, 248, 425, 875],
    discount: [5, 8, 14, 24, 46, 84, 124, 213, 438],
  },
  "2STEP": {
    real:   [8, 14, 25, 33, 69, 125, 188, 313, 985],
    discount: [4, 7, 13, 17, 35, 63, 94, 157, 493],
  },
};
const amounts = ["1K", "3K", "5K", "10K", "25K", "50K", "100K", "200K", "500K"] as const;
type AmountKey = typeof amounts[number];
const amountMap: Record<AmountKey, string> = {
  "1K": "$1K",
  "3K": "$3K",
  "5K": "$5K",
  "10K": "$10K",
  "25K": "$25K",
  "50K": "$50K",
  "100K": "$100K",
  "200K": "$200K",
  "500K": "$500K",
};

// For the popup, show 50K as the main offer, 25K and 100K as secondary offers
const mainIndex = 5; // 50K
const leftIndex = 4; // 25K
const rightIndex = 6; // 100K
const mainAmount = amounts[mainIndex];
const leftAmount = amounts[leftIndex];
const rightAmount = amounts[rightIndex];
// Use the exact real prices as requested
const mainOriginal = 125; // 50K real price
const mainDiscount = 50;  // 50K discounted price
const rightOriginal = 188; // 100K real price
const rightDiscount = 75;  // 100K discounted price
const leftOriginal = priceTable["2STEP"].real[leftIndex];
const leftDiscount = Math.round(leftOriginal * 0.4);

// Add a simple sparkle/confetti SVG for the 60% OFF tag
const Sparkle = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" className="inline-block align-middle animate-spin-slow" xmlns="http://www.w3.org/2000/svg">
    <g opacity="0.7">
      <circle cx="16" cy="16" r="2" fill="#fff200" />
      <circle cx="8" cy="8" r="1.2" fill="#ffb300" />
      <circle cx="24" cy="8" r="1.2" fill="#ffb300" />
      <circle cx="8" cy="24" r="1.2" fill="#ffb300" />
      <circle cx="24" cy="24" r="1.2" fill="#ffb300" />
      <circle cx="16" cy="4" r="1" fill="#fff200" />
      <circle cx="16" cy="28" r="1" fill="#fff200" />
      <circle cx="4" cy="16" r="1" fill="#fff200" />
      <circle cx="28" cy="16" r="1" fill="#fff200" />
    </g>
  </svg>
);

export default function HeroPopup() {
  const [open, setOpen] = useState(false)
  const [isPageLoaded, setIsPageLoaded] = useState(false)
  const [timer, setTimer] = useState(24 * 60 * 60) // 24 hours in seconds
  const [selectedPlan, setSelectedPlan] = useState("INSTANT")

  useEffect(() => {
    const handleLoad = () => setIsPageLoaded(true)
    if (document.readyState === 'complete') setIsPageLoaded(true)
    else window.addEventListener('load', handleLoad)
    return () => window.removeEventListener('load', handleLoad)
  }, [])

  useEffect(() => {
    if (isPageLoaded) {
      const t = setTimeout(() => setOpen(true), 4000)
      return () => clearTimeout(t)
    }
  }, [isPageLoaded])

  useEffect(() => {
    if (!open) return
    if (timer <= 0) return
    const interval = setInterval(() => setTimer(t => t - 1), 1000)
    return () => clearInterval(interval)
  }, [open, timer])

  if (!open) return null

  // Format timer as HH:MM:SS
  const formatTimer = (t: number) => {
    const h = String(Math.floor(t / 3600)).padStart(2, '0')
    const m = String(Math.floor((t % 3600) / 60)).padStart(2, '0')
    const s = String(t % 60).padStart(2, '0')
    return `${h}:${m}:${s}`
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-xl">
      <div className="relative w-full max-w-sm md:max-w-2xl mx-auto rounded-2xl overflow-hidden shadow-2xl border-2 border-sky-500 animate-gradient-move bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-[#001a2c] via-[#002a3c] to-[#002235]">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-white text-2xl z-10"
          onClick={() => setOpen(false)}
          aria-label="Close"
        >
          <X className="w-7 h-7" />
        </button>
        <div className="flex flex-col items-center justify-center p-4 md:p-8">
          <div className="text-center mb-6">
            <div className="flex items-center justify-center gap-2">
              <span className="text-2xl md:text-4xl font-extrabold text-white tracking-wide drop-shadow-lg">SUMMER SALE</span>
              <span className="relative">
                <span className="bg-gradient-to-r from-yellow-400 to-orange-400 text-transparent bg-clip-text text-3xl md:text-5xl font-black ml-2 drop-shadow-xl animate-pulse">60% OFF</span>
                <span className="absolute -top-4 -right-8 z-10 hidden md:block"><Sparkle /></span>
              </span>
            </div>
            <div className="text-base md:text-lg text-sky-300 font-semibold mb-2 mt-2">60% OFF on all accounts above $25K</div>
            <div className="text-sm md:text-base text-yellow-300 font-semibold mb-2">20% OFF on accounts below $50K</div>
            <div className="text-sm md:text-base text-gray-300 mb-2">Use Code: <span className="bg-[#002a3c] text-yellow-300 px-2 md:px-3 py-1 rounded font-mono">SUMMER60</span></div>
            <div className="flex items-center justify-center gap-2 text-white font-bold text-base md:text-lg mb-4">
              <span className="bg-gradient-to-r from-sky-500 to-blue-500 px-2 md:px-4 py-2 rounded-xl shadow-lg animate-timer-glow">Sale ends in <span className="font-mono">{formatTimer(timer)}</span></span>
            </div>
          </div>
          <div className="flex flex-row items-center justify-center gap-2 md:gap-4 w-full mb-6">
            {/* Left (25K) */}
            <div className="flex flex-col items-center justify-center bg-gradient-to-br from-[#002a3c] to-[#003a4c] rounded-xl p-2 md:p-4 shadow-md min-w-[80px] md:min-w-[120px] border-2 border-sky-700">
              <div className="text-lg font-bold text-sky-300 mb-1">{amountMap[leftAmount]}</div>
              <div className="text-xs text-gray-400 mb-0.5">Real Price: ${leftOriginal}</div>
              <div className="text-xs text-yellow-300 font-bold">Now: ${leftDiscount}</div>
            </div>
            {/* Center (50K main) */}
            <div className="flex flex-col items-center justify-center bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl p-3 md:p-6 shadow-2xl min-w-[120px] md:min-w-[200px] border-4 border-yellow-300 scale-105 md:scale-110 relative animate-glow">
              <div className="absolute inset-0 rounded-2xl border-4 border-yellow-200 animate-border-glow pointer-events-none" />
              <div className="text-2xl md:text-3xl font-black text-white mb-1 drop-shadow-xl">{amountMap[mainAmount]}</div>
              <div className="text-xs md:text-base text-white mb-1">Step 2 Challenge</div>
              <div className="text-base md:text-lg text-white mb-1 font-semibold">Real Price: <span className="line-through text-gray-200">${mainOriginal}</span></div>
              <div className="text-2xl md:text-4xl font-extrabold text-white mb-1 drop-shadow-xl">Now: <span className="text-yellow-300">${mainDiscount}</span></div>
              <div className="text-xs font-bold text-[#002a3c] bg-white/80 px-2 py-1 rounded mt-1 tracking-wider">MOST POPULAR</div>
            </div>
            {/* Right (100K) */}
            <div className="flex flex-col items-center justify-center bg-gradient-to-br from-[#002a3c] to-[#003a4c] rounded-xl p-2 md:p-4 shadow-md min-w-[80px] md:min-w-[120px] border-2 border-sky-700">
              <div className="text-lg font-bold text-sky-300 mb-1">{amountMap[rightAmount]}</div>
              <div className="text-xs text-gray-400 mb-0.5">Real Price: ${rightOriginal}</div>
              <div className="text-xs text-yellow-300 font-bold">Now: ${rightDiscount}</div>
            </div>
          </div>
          <Link href="/signup">
            <button className="w-full bg-gradient-to-r from-yellow-400 to-orange-400 text-white font-bold text-base md:text-lg py-3 rounded-lg mt-2 shadow-xl hover:scale-105 transition-all relative overflow-hidden shine-btn">
              <span className="relative z-10">Sign Up & Save Now</span>
              <span className="absolute left-0 top-0 w-full h-full pointer-events-none animate-shine" />
            </button>
          </Link>
        </div>
      </div>
      <style jsx global>{`
        @keyframes gradient-move {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        .animate-gradient-move {
          background-size: 200% 200%;
          animation: gradient-move 8s ease-in-out infinite;
        }
        .animate-glow {
          box-shadow: 0 0 40px 10px #ffe06699, 0 0 8px 2px #fff20066;
        }
        .animate-border-glow {
          animation: border-glow 2s infinite alternate;
        }
        @keyframes border-glow {
          0% { box-shadow: 0 0 0 0 #fff20044; }
          100% { box-shadow: 0 0 24px 8px #fff20099; }
        }
        .animate-spin-slow {
          animation: spin 4s linear infinite;
        }
        @keyframes spin {
          100% { transform: rotate(360deg); }
        }
        .animate-timer-glow {
          box-shadow: 0 0 16px 2px #38bdf8cc;
        }
        .shine-btn .animate-shine {
          background: linear-gradient(120deg, transparent 0%, #fff8 40%, #fff0 60%, transparent 100%);
          transform: translateX(-100%);
          animation: shine-move 2.5s linear infinite;
        }
        @keyframes shine-move {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  )
} 